import os
import time

# https://github.com/Yuliang-<PERSON>/MonkeyOCR/
from magic_pdf.model.custom_model import <PERSON>OC<PERSON>
from magic_pdf.data.data_reader_writer import FileBasedDataWriter, FileBasedDataReader
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model_llm import doc_analyze_llm

# https://github.com/Textualize/rich
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from lmdeploy.utils import get_logger

get_logger('lmdeploy').setLevel('ERROR')

console = Console()

"""
TODO
supprimer le repertoire si existant
"""

def main():
    try:
        console.print(
            Panel.fit(
                "[bold blue]🐒 MonkeyOCR - Conversion PDF vers Markdown[/bold blue]\n"
                "[dim]Utilisation du modèle MonkeyOCR pour l'analyse de documents[/dim]",
                border_style="blue",
            )
        )

        pdf_file_name = "../data/trombinoscope.pdf"
        name_without_suff = ".".join(os.path.basename(pdf_file_name).split(".")[:-1])

        script_dir = os.path.dirname(os.path.abspath(__file__))
        local_md_dir = os.path.join(script_dir, "output", name_without_suff)
        local_image_dir = os.path.join(local_md_dir, "images")

        os.makedirs(local_image_dir, exist_ok=True)

        console.print(f"📄 [cyan]Fichier PDF:[/cyan] {pdf_file_name}")
        console.print(f"📁 [cyan]Répertoire de sortie:[/cyan] {local_md_dir}")
        console.print(f"🖼️ [cyan]Répertoire des images:[/cyan] {local_image_dir}")

        image_writer, md_writer = (
            FileBasedDataWriter(local_image_dir),
            FileBasedDataWriter(local_md_dir),
        )

        reader1 = FileBasedDataReader()
        pdf_bytes = reader1.read(pdf_file_name)
        ds = PymuDocDataset(pdf_bytes)

        num_pages = len(ds)
        console.print(f"📊 [cyan]Nombre de pages détectées:[/cyan] {num_pages}")

        console.print("🚀 [bold green]Démarrage du traitement...[/bold green]")
        t1 = time.time()

        with console.status(
            "[bold green]Chargement du modèle MonkeyOCR...", spinner="dots"
        ):
            model_load_start = time.time()
            MonkeyOCR_model = MonkeyOCR("model_configs.yaml")
            model_load_time = time.time() - model_load_start

        console.print(
            f"✅ [green]Modèle MonkeyOCR chargé avec succès![/green] [dim]({model_load_time:.2f}s)[/dim]"
        )

        with console.status("[bold green]Analyse du document en cours...", spinner="dots"):
            analysis_start = time.time()
            infer_result = ds.apply(doc_analyze_llm, MonkeyOCR_model=MonkeyOCR_model)
            analysis_time = time.time() - analysis_start

            ocr_start = time.time()
            pipe_result = infer_result.pipe_ocr_mode(
                image_writer, MonkeyOCR_model=MonkeyOCR_model
            )
            ocr_time = time.time() - ocr_start

        single_time = time.time() - t1
        avg_time_per_page = single_time / num_pages if num_pages > 0 else 0

        timing_table = Table(title="Statistiques de traitement")
        timing_table.add_column("Étape", style="cyan")
        timing_table.add_column("Temps (s)", style="green")
        timing_table.add_column("Pourcentage", style="yellow")

        timing_table.add_row(
            "Chargement du modèle",
            f"{model_load_time:.2f}",
            f"{(model_load_time / single_time) * 100:.1f}%",
        )
        timing_table.add_row(
            "Analyse du document",
            f"{analysis_time:.2f}",
            f"{(analysis_time / single_time) * 100:.1f}%",
        )
        timing_table.add_row(
            "OCR et traitement", f"{ocr_time:.2f}", f"{(ocr_time / single_time) * 100:.1f}%"
        )
        timing_table.add_row("Total", f"{single_time:.2f}", "100.0%")

        console.print(timing_table)

        console.print(f"⏱️  [bold cyan]Temps de traitement:[/bold cyan] {single_time:.2f}s")
        console.print(
            f"📈 [bold cyan]Temps moyen par page:[/bold cyan] {avg_time_per_page:.2f}s"
        )

        output_md_path = os.path.join(local_md_dir, f"monkeyoc_{name_without_suff}.md")
        pipe_result.dump_md(md_writer, output_md_path, local_image_dir)

        console.print(
            f"💾 [bold green]Résultat sauvegardé:[/bold green] [link]{output_md_path}[/link]"
        )
        console.print(
            Panel.fit(
                f"[bold green]🎉 Conversion terminée avec succès![/bold green]\n"
                f"📄 Fichier de sortie: [cyan]{os.path.basename(output_md_path)}[/cyan]\n"
                f"📊 Nombre de pages: [yellow]{num_pages}[/yellow]\n"
                f"⏱️ Temps total: [yellow]{single_time:.2f}s[/yellow]\n"
                f"📈 Temps moyen par page: [yellow]{avg_time_per_page:.2f}s[/yellow]",
                border_style="green",
            )
        )
    except KeyboardInterrupt:
        return

if __name__ == "__main__":
    main()
